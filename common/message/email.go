package message

import (
	"bytes"
	"context"
	"crypto/rand"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/utils"
	"net"
	"net/http"
	"net/smtp"
	"strings"
	"time"
)

func shouldAuth() bool {
	return config.SMTPAccount != "" || config.SMTPToken != ""
}

func SendEmail(subject string, receiver string, content string) error {
	// 判断是否需要代理
	if config.EmailProxyServer != "" && !config.EmailProxyExecutor {
		requestUrl := fmt.Sprintf("%s/api/proxy_email", config.EmailProxyServer)

		reqBody := common.ProxyEmailRequest{
			SystemName:  config.SystemName,
			Email:       receiver,
			Subject:     subject,
			Content:     content,
			SMTPServer:  config.SMTPServer,
			SMTPPort:    config.SMTPPort,
			SMTPAccount: config.SMTPAccount,
			SMTPFrom:    config.SMTPFrom,
			SMTPToken:   config.SMTPToken,
		}
		body, err := json.Marshal(reqBody)
		if err != nil {
			logger.SysError(fmt.Sprintf("proxy_email error: %v", err))
			return err
		}

		req, err := http.NewRequest("POST", requestUrl, bytes.NewBuffer(body))
		if err != nil {
			logger.SysError(fmt.Sprintf("Get Task error: %v", err))
		}
		// 设置超时时间
		timeout := time.Second * 30
		ctx, cancel := context.WithTimeout(context.Background(), timeout)
		defer cancel()
		// 使用带有超时的 context 创建新的请求
		req = req.WithContext(ctx)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", config.EmailProxyAuth)
		resp, err := utils.HTTPClient.Do(req)
		if err != nil {
			logger.SysError(fmt.Sprintf("proxy_email error: %v", err))
			return err
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			logger.SysError(fmt.Sprintf("proxy_email error: %v", err))
			return fmt.Errorf("proxy_email error: %v", err)
		}
		return nil
	}

	if receiver == "" {
		return fmt.Errorf("receiver is empty")
	}
	if config.SMTPFrom == "" { // for compatibility
		config.SMTPFrom = config.SMTPAccount
	}
	encodedSubject := fmt.Sprintf("=?UTF-8?B?%s?=", base64.StdEncoding.EncodeToString([]byte(subject)))

	// Extract domain from SMTPFrom
	parts := strings.Split(config.SMTPFrom, "@")
	var domain string
	if len(parts) > 1 {
		domain = parts[1]
	}
	// Generate a unique Message-ID
	buf := make([]byte, 16)
	_, err := rand.Read(buf)
	if err != nil {
		return err
	}
	messageId := fmt.Sprintf("<%x@%s>", buf, domain)

	mail := []byte(fmt.Sprintf("To: %s\r\n"+
		"From: %s<%s>\r\n"+
		"Subject: %s\r\n"+
		"Message-ID: %s\r\n"+ // add Message-ID header to avoid being treated as spam, RFC 5322
		"Date: %s\r\n"+
		"Content-Type: text/html; charset=UTF-8\r\n\r\n%s\r\n",
		receiver, config.SystemName, config.SMTPFrom, encodedSubject, messageId, time.Now().Format(time.RFC1123Z), content))

	auth := smtp.PlainAuth("", config.SMTPAccount, config.SMTPToken, config.SMTPServer)
	addr := fmt.Sprintf("%s:%d", config.SMTPServer, config.SMTPPort)
	to := strings.Split(receiver, ";")

	if config.SMTPPort == 465 || !shouldAuth() {
		// need advanced client
		var conn net.Conn
		var err error
		if config.SMTPPort == 465 {
			tlsConfig := &tls.Config{
				InsecureSkipVerify: true,
				ServerName:         config.SMTPServer,
			}
			conn, err = tls.Dial("tcp", fmt.Sprintf("%s:%d", config.SMTPServer, config.SMTPPort), tlsConfig)
		} else {
			conn, err = net.Dial("tcp", fmt.Sprintf("%s:%d", config.SMTPServer, config.SMTPPort))
		}
		if err != nil {
			return err
		}
		client, err := smtp.NewClient(conn, config.SMTPServer)
		if err != nil {
			return err
		}
		defer client.Close()
		if shouldAuth() {
			if err = client.Auth(auth); err != nil {
				return err
			}
		}
		if err = client.Mail(config.SMTPFrom); err != nil {
			return err
		}
		receiverEmails := strings.Split(receiver, ";")
		for _, receiver := range receiverEmails {
			if err = client.Rcpt(receiver); err != nil {
				return err
			}
		}
		w, err := client.Data()
		if err != nil {
			return err
		}
		_, err = w.Write(mail)
		if err != nil {
			return err
		}
		err = w.Close()
		if err != nil {
			return err
		}
		return nil
	}
	err = smtp.SendMail(addr, auth, config.SMTPAccount, to, mail)
	if err != nil && strings.Contains(err.Error(), "short response") { // 部分提供商返回该错误，但实际上邮件已经发送成功
		logger.SysWarnf("short response from SMTP server, return nil instead of error: %s", err.Error())
		return nil
	}
	return err
}

func SendProxyEmail(systemName string, receiver string, subject string, content string, smtpServer string, smtpPort int, smtpAccount string, smtpFrom string, smtpToken string) error {
	if receiver == "" {
		return fmt.Errorf("receiver is empty")
	}
	if smtpFrom == "" { // for compatibility
		smtpFrom = smtpAccount
	}
	if systemName == "" {
		systemName = config.SystemName
	}
	encodedSubject := fmt.Sprintf("=?UTF-8?B?%s?=", base64.StdEncoding.EncodeToString([]byte(subject)))

	// Extract domain from SMTPFrom
	parts := strings.Split(smtpFrom, "@")
	var domain string
	if len(parts) > 1 {
		domain = parts[1]
	}
	// Generate a unique Message-ID
	buf := make([]byte, 16)
	_, err := rand.Read(buf)
	if err != nil {
		return err
	}
	messageId := fmt.Sprintf("<%x@%s>", buf, domain)

	mail := []byte(fmt.Sprintf("To: %s\r\n"+
		"From: %s<%s>\r\n"+
		"Subject: %s\r\n"+
		"Message-ID: %s\r\n"+ // add Message-ID header to avoid being treated as spam, RFC 5322
		"Date: %s\r\n"+
		"Content-Type: text/html; charset=UTF-8\r\n\r\n%s\r\n",
		receiver, systemName, smtpFrom, encodedSubject, messageId, time.Now().Format(time.RFC1123Z), content))
	auth := smtp.PlainAuth("", smtpAccount, smtpToken, smtpServer)
	addr := fmt.Sprintf("%s:%d", smtpServer, smtpPort)
	to := strings.Split(receiver, ";")

	if smtpPort == 465 {
		tlsConfig := &tls.Config{
			InsecureSkipVerify: true,
			ServerName:         smtpServer,
		}
		conn, err := tls.Dial("tcp", fmt.Sprintf("%s:%d", smtpServer, smtpPort), tlsConfig)
		if err != nil {
			return err
		}
		client, err := smtp.NewClient(conn, smtpServer)
		if err != nil {
			return err
		}
		defer client.Close()
		if err = client.Auth(auth); err != nil {
			return err
		}
		if err = client.Mail(smtpFrom); err != nil {
			return err
		}
		receiverEmails := strings.Split(receiver, ";")
		for _, receiver := range receiverEmails {
			if err = client.Rcpt(receiver); err != nil {
				return err
			}
		}
		w, err := client.Data()
		if err != nil {
			return err
		}
		_, err = w.Write(mail)
		if err != nil {
			return err
		}
		err = w.Close()
		if err != nil {
			return err
		}
	} else {
		err = smtp.SendMail(addr, auth, smtpAccount, to, mail)
	}
	return err
}
