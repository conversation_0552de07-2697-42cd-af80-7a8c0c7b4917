package middleware

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
)

// UserTimeoutMiddleware 用户超时中间件
func UserTimeoutMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查全局开关
		if !config.UserTimeoutEnabled {
			c.Next()
			return
		}

		// 只对relay请求进行超时检查
		if !isRelayRequest(c.Request.URL.Path) {
			c.Next()
			return
		}

		userId := c.GetInt(ctxkey.Id)
		if userId == 0 {
			c.Next()
			return
		}

		// 获取模型名称
		requestModel := c.GetString(ctxkey.RequestModel)
		if requestModel == "" {
			// 尝试从请求体中获取模型名称（这里可能需要在之前的中间件中解析）
			requestModel = extractModelFromRequest(c)
		}

		if requestModel == "" {
			c.Next()
			return
		}

		// 优先从内存缓存获取用户超时配置
		timeoutConfig := GetUserTimeoutConfigFromCache(userId, requestModel)
		if timeoutConfig == nil {
			// 没有自定义超时配置，使用默认流程
			c.Next()
			return
		}

		// 使用超时包装器进行强制超时控制
		totalTimeout := time.Duration(timeoutConfig.TotalTimeout) * time.Second
		timeoutWrapper := NewTimeoutWrapper(c.Request.Context(), totalTimeout, userId, requestModel)

		// 设置首字节超时（如果配置了）
		if timeoutConfig.FirstByteTimeout > 0 {
			firstByteTimeout := time.Duration(timeoutConfig.FirstByteTimeout) * time.Second
			timeoutWrapper.SetFirstByteTimeout(firstByteTimeout)
		}

		// 存储超时配置和包装器
		c.Set("user_timeout_config", timeoutConfig)
		c.Set("timeout_wrapper", timeoutWrapper)

		// 检查费用承担方设置
		if timeoutConfig.TimeoutCostBearer == "admin" {
			c.Set("admin_bears_timeout_cost", true)
		}

		// 确保在函数结束时清理资源
		defer timeoutWrapper.Cleanup()

		logger.Debugf(c.Request.Context(),
			"Starting request with user timeout: user=%d model=%s total_timeout=%ds first_byte_timeout=%ds cost_bearer=%s",
			userId, requestModel, timeoutConfig.TotalTimeout, timeoutConfig.FirstByteTimeout, timeoutConfig.TimeoutCostBearer)

		// 使用超时包装器执行请求处理
		execErr := timeoutWrapper.Execute(c, func() {
			c.Next()
		})

		if execErr != nil {
			// 发生了超时或其他错误
			c.Set("user_timeout_occurred", true)
			c.Set("user_timeout_duration", timeoutConfig.TotalTimeout)

			logger.Warnf(c.Request.Context(),
				"Request terminated due to timeout: user=%d model=%s total_timeout=%ds first_byte_timeout=%ds error=%v",
				userId, requestModel, timeoutConfig.TotalTimeout, timeoutConfig.FirstByteTimeout, execErr)
		} else {
			logger.Debugf(c.Request.Context(),
				"Request completed successfully: user=%d model=%s",
				userId, requestModel)
		}
	}
}

// isRelayRequest 检查是否是relay请求
func isRelayRequest(path string) bool {
	relayPaths := []string{
		"/v1/chat/completions",
		"/v1/completions",
		"/v1/images/generations",
		"/v1/images/edits",
		"/v1/audio/speech",
		"/v1/audio/transcriptions",
		"/v1/audio/translations",
		"/v1/embeddings",
	}

	for _, relayPath := range relayPaths {
		if path == relayPath {
			return true
		}
	}
	return false
}

// extractModelFromRequest 从请求中提取模型名称
func extractModelFromRequest(c *gin.Context) string {
	// 可以从header中获取，或者从已解析的context中获取
	if model := c.GetString("model"); model != "" {
		return model
	}

	// 如果都获取不到，返回通用配置标识
	return "*"
}

// GetUserTimeoutConfigFromCache 从内存缓存中获取用户超时配置
func GetUserTimeoutConfigFromCache(userId int, modelName string) *config.UserTimeoutConfig {
	// 优先查找用户+模型的精确配置
	specificKey := fmt.Sprintf("%d:%s", userId, modelName)
	if value, ok := config.UserTimeoutConfigsMap.Load(specificKey); ok {
		if specificConfig, ok := value.(*config.UserTimeoutConfig); ok {
			return specificConfig
		}
	}

	// 再查找用户+通用(*)配置
	genericKey := fmt.Sprintf("%d:*", userId)
	if value, ok := config.UserTimeoutConfigsMap.Load(genericKey); ok {
		if genericConfig, ok := value.(*config.UserTimeoutConfig); ok {
			return genericConfig
		}
	}

	// 没有找到任何配置
	return nil
}

// ==== 以下是保留的原有结构体和方法，用于兼容性 ====

// TimeoutChecker 超时检查结构体
type TimeoutChecker struct {
	userId           int
	modelName        string
	timeoutConfig    *model.UserTimeoutConfig
	firstByteTimer   *time.Timer
	totalTimer       *time.Timer
	tpsChecker       *TPSChecker
	requestStartTime time.Time
	firstByteTime    *time.Time
	isStream         bool
	cancelled        bool
	cancelFunc       context.CancelFunc
	config           *model.UserTimeoutConfig
}

// TPSChecker TPS检查器
type TPSChecker struct {
	startTime     time.Time
	tokenCount    int
	threshold     float64
	lastCheckTime time.Time
}

// NewTimeoutChecker 创建超时检查器
func NewTimeoutChecker(userId int, modelName string, isStream bool) (*TimeoutChecker, error) {
	// 使用新的内存缓存
	timeoutConfig := GetUserTimeoutConfigFromCache(userId, modelName)
	if timeoutConfig == nil {
		return nil, fmt.Errorf("no timeout configuration found for user %d model %s", userId, modelName)
	}

	// 转换为model.UserTimeoutConfig类型
	modelConfig := &model.UserTimeoutConfig{
		UserId:            timeoutConfig.UserId,
		ModelName:         timeoutConfig.ModelName,
		FirstByteTimeout:  timeoutConfig.FirstByteTimeout,
		TotalTimeout:      timeoutConfig.TotalTimeout,
		TpsThreshold:      timeoutConfig.TpsThreshold,
		TimeoutCostBearer: timeoutConfig.TimeoutCostBearer,
		Enabled:           true, // 缓存中的配置默认启用
	}

	checker := &TimeoutChecker{
		userId:           userId,
		modelName:        modelName,
		timeoutConfig:    modelConfig,
		requestStartTime: time.Now(),
		isStream:         isStream,
		cancelled:        false,
		config:           modelConfig,
	}

	// 初始化TPS检查器
	if timeoutConfig.TpsThreshold > 0 {
		checker.tpsChecker = &TPSChecker{
			threshold:     timeoutConfig.TpsThreshold,
			startTime:     time.Now(),
			lastCheckTime: time.Now(),
		}
	}

	return checker, nil
}

// StartTimeoutMonitoring 开始超时监控
func (tc *TimeoutChecker) StartTimeoutMonitoring(ctx context.Context, c *gin.Context) context.Context {
	// 创建带超时的context
	newCtx, cancel := context.WithCancel(ctx)
	tc.cancelFunc = cancel

	// 设置总超时定时器
	tc.totalTimer = time.AfterFunc(time.Duration(tc.timeoutConfig.TotalTimeout)*time.Second, func() {
		if !tc.cancelled {
			tc.cancelled = true
			logger.Warnf(newCtx, "User %d model %s total timeout after %d seconds",
				tc.userId, tc.modelName, tc.timeoutConfig.TotalTimeout)

			// 设置超时标记
			c.Set("user_timeout_type", "total")
			c.Set("user_timeout_threshold", tc.timeoutConfig.TotalTimeout)

			// 取消请求
			cancel()
		}
	})

	return newCtx
}

// OnFirstByte 在收到第一个字节时调用
func (tc *TimeoutChecker) OnFirstByte() {
	if tc.firstByteTime == nil {
		now := time.Now()
		tc.firstByteTime = &now

		// 停止首字超时定时器
		if tc.firstByteTimer != nil {
			tc.firstByteTimer.Stop()
		}

		firstByteDuration := now.Sub(tc.requestStartTime)
		logger.Debugf(context.Background(), "User %d model %s first byte received after %v",
			tc.userId, tc.modelName, firstByteDuration)
	}
}

// CheckTPS 检查TPS是否超过阈值
func (tc *TimeoutChecker) CheckTPS(newTokens int) bool {
	if tc.tpsChecker == nil || tc.tpsChecker.threshold <= 0 {
		return true // 不限制TPS
	}

	tc.tpsChecker.tokenCount += newTokens
	now := time.Now()
	duration := now.Sub(tc.tpsChecker.startTime).Seconds()

	if duration > 0 {
		currentTPS := float64(tc.tpsChecker.tokenCount) / duration
		if currentTPS < tc.tpsChecker.threshold {
			// TPS过低，取消请求
			logger.Warnf(context.Background(), "User %d model %s TPS %.2f below threshold %.2f",
				tc.userId, tc.modelName, currentTPS, tc.tpsChecker.threshold)
			return false
		}
	}

	return true
}

// Cleanup 清理资源
func (tc *TimeoutChecker) Cleanup() {
	if tc.firstByteTimer != nil {
		tc.firstByteTimer.Stop()
	}
	if tc.totalTimer != nil {
		tc.totalTimer.Stop()
	}
	if tc.cancelFunc != nil {
		tc.cancelFunc()
	}
	tc.cancelled = true
}

// GetTpsThreshold 获取TPS阈值
func (tc *TimeoutChecker) GetTpsThreshold() float64 {
	if tc.config == nil {
		return 0.0
	}
	return tc.config.TpsThreshold
}

// GetFirstByteTimeout 获取首字节超时时间
func (tc *TimeoutChecker) GetFirstByteTimeout() int {
	if tc.config == nil {
		return 0
	}
	return tc.config.FirstByteTimeout
}

// GetTotalTimeout 获取总超时时间
func (tc *TimeoutChecker) GetTotalTimeout() int {
	if tc.config == nil {
		return 0
	}
	return tc.config.TotalTimeout
}

// GetUserId 获取用户ID
func (tc *TimeoutChecker) GetUserId() int {
	if tc.config == nil {
		return 0
	}
	return tc.config.UserId
}

// GetModelName 获取模型名称
func (tc *TimeoutChecker) GetModelName() string {
	if tc.config == nil {
		return ""
	}
	return tc.config.ModelName
}

// GetTimeoutCostBearer 获取超时费用承担方
func (tc *TimeoutChecker) GetTimeoutCostBearer() string {
	if tc.config == nil {
		return "user" // 默认用户承担
	}
	if tc.config.TimeoutCostBearer == "" {
		return "user" // 默认用户承担
	}
	return tc.config.TimeoutCostBearer
}

// TimeoutWrapper 超时包装器，用于强制终止请求
type TimeoutWrapper struct {
	ctx               context.Context
	cancel            context.CancelFunc
	done              chan bool
	timeout           time.Duration
	firstByteTimeout  time.Duration
	userId            int
	modelName         string
	firstByteTimer    *time.Timer
	firstByteReceived bool
	mu                sync.RWMutex
}

// NewTimeoutWrapper 创建超时包装器
func NewTimeoutWrapper(ctx context.Context, timeout time.Duration, userId int, modelName string) *TimeoutWrapper {
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	return &TimeoutWrapper{
		ctx:               timeoutCtx,
		cancel:            cancel,
		done:              make(chan bool, 1),
		timeout:           timeout,
		userId:            userId,
		modelName:         modelName,
		firstByteReceived: false,
	}
}

// SetFirstByteTimeout 设置首字节超时
func (tw *TimeoutWrapper) SetFirstByteTimeout(firstByteTimeout time.Duration) {
	tw.firstByteTimeout = firstByteTimeout
}

// OnFirstByteReceived 标记首字节已收到
func (tw *TimeoutWrapper) OnFirstByteReceived() {
	tw.mu.Lock()
	defer tw.mu.Unlock()

	if !tw.firstByteReceived {
		tw.firstByteReceived = true
		if tw.firstByteTimer != nil {
			tw.firstByteTimer.Stop()
		}
		logger.Debugf(tw.ctx, "First byte received for user %d model %s", tw.userId, tw.modelName)
	}
}

// Execute 执行请求处理，带强制超时
func (tw *TimeoutWrapper) Execute(c *gin.Context, next func()) error {
	// 设置带超时的context
	c.Request = c.Request.WithContext(tw.ctx)

	// 创建响应写入标记
	responseSent := make(chan bool, 1)

	// 创建一个goroutine来执行实际的请求处理
	var processingErr error
	processingDone := make(chan bool, 1)

	// 启动首字节超时定时器（如果配置了首字节超时）
	if tw.firstByteTimeout > 0 {
		tw.firstByteTimer = time.AfterFunc(tw.firstByteTimeout, func() {
			tw.mu.RLock()
			firstByteReceived := tw.firstByteReceived
			tw.mu.RUnlock()

			if !firstByteReceived {
				logger.Warnf(tw.ctx, "First byte timeout for user %d model %s after %v",
					tw.userId, tw.modelName, tw.firstByteTimeout)

				// 设置首字节超时标记
				c.Set("user_timeout_type", "first_byte")
				c.Set("user_timeout_threshold", int(tw.firstByteTimeout.Seconds()))
				c.Set("user_timeout_occurred", true)

				// 强制终止请求
				c.Abort()

				// 发送首字节超时响应
				select {
				case responseSent <- true:
					if !c.Writer.Written() {
						tw.sendFirstByteTimeoutResponse(c)
					}
				default:
					// 响应可能已经在发送中
				}

				// 取消context
				tw.cancel()
			}
		})
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf(tw.ctx, "Request processing panic for user %d model %s: %v",
					tw.userId, tw.modelName, r)
				processingErr = fmt.Errorf("request processing panic: %v", r)
			}
			processingDone <- true
		}()

		// 执行实际的请求处理
		next()
	}()

	// 等待处理完成或超时
	select {
	case <-processingDone:
		// 正常完成
		tw.done <- true
		return processingErr

	case <-tw.ctx.Done():
		// 超时发生
		if tw.ctx.Err() == context.DeadlineExceeded {
			logger.Warnf(tw.ctx, "Force terminating request for user %d model %s after %v timeout",
				tw.userId, tw.modelName, tw.timeout)

			// 强制终止请求处理
			c.Abort()

			// 尝试发送超时响应（非阻塞方式）
			select {
			case responseSent <- true:
				// 检查响应是否已经开始写入
				if !c.Writer.Written() {
					tw.sendTimeoutResponse(c)
				} else {
					logger.Warnf(tw.ctx, "Response already started for user %d model %s, cannot send timeout response",
						tw.userId, tw.modelName)
				}
			default:
				// 如果无法获取响应锁，说明可能已经在发送响应
				logger.Debugf(tw.ctx, "Response may already be in progress for user %d model %s",
					tw.userId, tw.modelName)
			}

			return fmt.Errorf("request timeout after %v", tw.timeout)
		}
		return tw.ctx.Err()
	}
}

// sendFirstByteTimeoutResponse 发送首字节超时响应
func (tw *TimeoutWrapper) sendFirstByteTimeoutResponse(c *gin.Context) {
	// 设置响应头
	c.Header("Connection", "close")
	c.Header("Content-Type", "application/json")
	c.Header("Cache-Control", "no-cache")

	timeoutError := gin.H{
		"error": gin.H{
			"code":    "first_byte_timeout",
			"message": fmt.Sprintf("First byte timeout after %.0f seconds", tw.firstByteTimeout.Seconds()),
			"type":    "user_custom_timeout",
			"details": gin.H{
				"user_id":      tw.userId,
				"model_name":   tw.modelName,
				"timeout":      tw.firstByteTimeout.Seconds(),
				"timeout_type": "first_byte",
			},
		},
	}

	c.JSON(http.StatusTooManyRequests, timeoutError)

	logger.Infof(tw.ctx, "Sent first byte timeout response for user %d model %s after %v",
		tw.userId, tw.modelName, tw.firstByteTimeout)
}

// sendTimeoutResponse 发送超时响应
func (tw *TimeoutWrapper) sendTimeoutResponse(c *gin.Context) {
	// 设置响应头
	c.Header("Connection", "close")
	c.Header("Content-Type", "application/json")
	c.Header("Cache-Control", "no-cache")

	timeoutError := gin.H{
		"error": gin.H{
			"code":    "request_timeout",
			"message": fmt.Sprintf("Request timeout after %.0f seconds", tw.timeout.Seconds()),
			"type":    "user_custom_timeout",
			"details": gin.H{
				"user_id":    tw.userId,
				"model_name": tw.modelName,
				"timeout":    tw.timeout.Seconds(),
			},
		},
	}

	c.JSON(http.StatusTooManyRequests, timeoutError)

	logger.Infof(tw.ctx, "Sent timeout response for user %d model %s after %v",
		tw.userId, tw.modelName, tw.timeout)
}

// Cleanup 清理资源
func (tw *TimeoutWrapper) Cleanup() {
	if tw.firstByteTimer != nil {
		tw.firstByteTimer.Stop()
	}
	tw.cancel()
	if tw.done != nil {
		close(tw.done)
	}
}

// IsContextTimeout 检查context是否已超时
func IsContextTimeout(ctx context.Context) bool {
	select {
	case <-ctx.Done():
		return ctx.Err() == context.DeadlineExceeded
	default:
		return false
	}
}

// GetRemainingTimeout 获取剩余超时时间
func GetRemainingTimeout(ctx context.Context) time.Duration {
	if deadline, ok := ctx.Deadline(); ok {
		remaining := time.Until(deadline)
		if remaining < 0 {
			return 0
		}
		return remaining
	}
	return 0
}

// WithTimeoutCheck 为处理函数添加超时检查
func WithTimeoutCheck(ctx context.Context, handler func() error) error {
	// 创建一个带缓冲的通道来接收结果
	resultChan := make(chan error, 1)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				resultChan <- fmt.Errorf("handler panic: %v", r)
			}
		}()
		resultChan <- handler()
	}()

	select {
	case err := <-resultChan:
		return err
	case <-ctx.Done():
		return ctx.Err()
	}
}

// NotifyFirstByteReceived 从context中通知首字节已收到
func NotifyFirstByteReceived(c *gin.Context) {
	if wrapper, exists := c.Get("timeout_wrapper"); exists {
		if timeoutWrapper, ok := wrapper.(*TimeoutWrapper); ok {
			timeoutWrapper.OnFirstByteReceived()
		}
	}
}

// GetTimeoutWrapper 从context中获取超时包装器
func GetTimeoutWrapper(c *gin.Context) *TimeoutWrapper {
	if wrapper, exists := c.Get("timeout_wrapper"); exists {
		if timeoutWrapper, ok := wrapper.(*TimeoutWrapper); ok {
			return timeoutWrapper
		}
	}
	return nil
}

// IsFirstByteTimeoutEnabled 检查是否启用了首字节超时
func IsFirstByteTimeoutEnabled(c *gin.Context) bool {
	wrapper := GetTimeoutWrapper(c)
	return wrapper != nil && wrapper.firstByteTimeout > 0
}
