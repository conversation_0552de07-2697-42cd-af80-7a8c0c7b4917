package controller

import (
	"bytes"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/network"
	"github.com/songquanpeng/one-api/common/random"
	"github.com/songquanpeng/one-api/model"
)

func GetAllTokens(c *gin.Context) {
	userId := c.GetInt(ctxkey.Id)
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	order := c.Query("order")
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	name := c.Query("name")
	inputUserId, _ := strconv.Atoi(c.Query("user_id"))
	status, _ := strconv.Atoi(c.Query("status"))
	group := c.Query("models")
	billingType, _ := strconv.Atoi(c.Query(ctxkey.BillingType))
	key := c.Query("key")
	// 如果是sk-开头, 去掉sk-
	if key != "" && len(key) > 3 && key[:3] == "sk-" {
		key = key[3:]
	}
	// 如果当前用户是root则可以查看指定user的令牌
	if inputUserId != 0 {
		userById, err := model.GetUserById(userId, false)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		if userById.Role == common.RoleRootUser {
			userId = inputUserId
		} else {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无权查看其他用户的令牌",
			})
			return
		}
	}
	tokens, err := model.GetAllUserTokens(userId, p*pageSize, pageSize, order, name, status, group, billingType, key)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    tokens,
	})
	return
}

func CountTokens(c *gin.Context) {
	userId := c.GetInt("id")
	name := c.Query("name")
	status, _ := strconv.Atoi(c.Query("status"))
	group := c.Query("models")
	billingType, _ := strconv.Atoi(c.Query(ctxkey.BillingType))
	key := c.Query("key")
	// 如果是sk-开头, 去掉sk-
	if key != "" && len(key) > 3 && key[:3] == "sk-" {
		key = key[3:]
	}
	count, err := model.CountTokens(userId, name, status, group, billingType, key)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    0,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
	return
}

func SearchTokens(c *gin.Context) {
	userId := c.GetInt(ctxkey.Id)
	keyword := c.Query("keyword")
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	tokens, err := model.SearchUserTokens(userId, keyword, p*pageSize, pageSize)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    tokens,
	})
	return
}

func AdminTopUpToken(c *gin.Context) {
	ctx := c.Request.Context()
	var req AdminTopUpRequest
	err := json.NewDecoder(c.Request.Body).Decode(&req)
	if err != nil || req.Id == 0 || req.Quota == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	token, err := model.GetTokenById(req.Id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取令牌信息",
		})
		return
	}
	//这里要对正负数单独处理
	if !token.UnlimitedQuota {
		originalQuota := token.RemainQuota // 先记录原始余额
		err = model.TopupTokenQuota(token.Id, int64(req.Quota))
		if req.Quota > 0 {
			model.RecordLog(ctx, token.UserId, model.LogTypeTopup, fmt.Sprintf("管理员为用户 %d 令牌 %s 充值 %s (充值前余额: %s → 充值后余额: %s)（原因：%s）",
				token.UserId,
				token.Name,
				common.LogQuota(int64(req.Quota)),
				common.LogQuota(originalQuota),
				common.LogQuota(originalQuota+int64(req.Quota)),
				req.Reason))
		} else {
			model.RecordLog(ctx, token.UserId, model.LogTypeManage, fmt.Sprintf("管理员为用户 %d 令牌 %s 扣除 %s (扣除前余额: %s → 扣除后余额: %s)（原因：%s）",
				token.UserId,
				token.Name,
				common.LogQuota(int64(-req.Quota)),
				common.LogQuota(originalQuota),
				common.LogQuota(originalQuota+int64(req.Quota)),
				req.Reason))
		}
	} else {
		// 无限额度无需充值
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "令牌已无限额度，无需充值",
		})
		return
	}
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "充值失败" + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "AdminTopUpToken操作成功",
	})
	return
}

func ExportTokens(c *gin.Context) {
	userId := c.GetInt("id")
	keyword := c.Query("keyword")
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	tokens, err := model.SearchUserTokens(userId, keyword, p*pageSize, pageSize)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	// 步骤2: 创建CSV数据
	b := &bytes.Buffer{}
	w := csv.NewWriter(b)
	thead := []string{"令牌名称", "令牌密钥", "令牌状态", "可用模型", "已用额度$", "剩余额度$", "创建时间"}
	err = w.Write(thead)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	// 定义一个statusMap
	statusMap := map[int]string{
		1: "已启用",
		2: "已禁用",
		3: "已过期",
		4: "已耗尽",
	}
	for _, token := range tokens {
		models := token.Models
		if models == "" {
			models = "无限制"
		}
		row := []string{
			token.Name,
			"sk-" + token.Key,
			statusMap[token.Status],
			models,
			fmt.Sprintf("%.2f", float64(token.UsedQuota)/500000.0),
			fmt.Sprintf("%.2f", float64(token.RemainQuota)/500000.0),
			time.Unix(token.CreatedTime, 0).Format("2006-01-02 15:04:05"),
		}
		if err := w.Write(row); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
	}
	w.Flush()

	// 步骤3: 发送CSV文件
	c.Writer.Header().Set("Content-Type", "text/csv")
	c.Writer.Header().Set("Content-Disposition", "attachment; filename=tokenExport.csv")
	c.String(http.StatusOK, b.String())
	return
}

func GetDefaultToken(c *gin.Context) {
	userId := c.GetInt("id")
	token, err := model.GetUserInitialToken(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if token == nil || token.Id == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "未找到初始令牌",
		})
		return
	}
	tokenExtend, _ := model.GetTokenExtendByTokenId(token.Id)
	rsMap, err := helper.MergeStructsToMap(token, tokenExtend, "id", "token_id")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rsMap,
	})
	return
}

func GetToken(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	userId := c.GetInt(ctxkey.Id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	token, err := model.GetTokenByIds(id, userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	tokenExtend, _ := model.GetTokenExtendByTokenId(token.Id)
	rsMap, err := helper.MergeStructsToMap(token, tokenExtend, "id", "token_id")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rsMap,
	})
	return
}

func GetTokenStatus(c *gin.Context) {
	tokenId := c.GetInt(ctxkey.TokenId)
	userId := c.GetInt(ctxkey.Id)
	token, err := model.GetTokenByIds(tokenId, userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	expiredAt := token.ExpiredTime
	if expiredAt == -1 {
		expiredAt = 0
	}
	c.JSON(http.StatusOK, gin.H{
		"object":          "credit_summary",
		"total_granted":   token.RemainQuota,
		"total_used":      0, // not supported currently
		"total_available": token.RemainQuota,
		"expires_at":      expiredAt * 1000,
	})
}

func validateToken(c *gin.Context, token model.Token) error {
	if len(token.Name) > 30 {
		return fmt.Errorf("令牌名称过长")
	}
	if token.Subnet != nil && *token.Subnet != "" {
		err := network.IsValidSubnets(*token.Subnet)
		if err != nil {
			return fmt.Errorf("无效的网段：%s", err.Error())
		}
	}
	return nil
}

func AddToken(c *gin.Context) {
	ctx := c.Request.Context()
	var token model.Token
	var tokenExtend model.TokenExtend
	err := common.BindAndDecodeMainAndExtend(c, &token, &tokenExtend)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	err = validateToken(c, token)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("参数错误：%s", err.Error()),
		})
		return
	}

	// 处理 MJ 翻译配置的验证
	if tokenExtend.MjTranslateEnabled {
		if tokenExtend.MjTranslateBaseUrl == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "启用MJ翻译时必须提供翻译服务的基础URL",
			})
			return
		}
		if tokenExtend.MjTranslateApiKey == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "启用MJ翻译时必须提供翻译服务的API密钥",
			})
			return
		}
		if tokenExtend.MjTranslateModel == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "启用MJ翻译时必须提供翻译服务使用的模型",
			})
			return
		}
	}

	cleanToken := model.Token{
		UserId:         c.GetInt(ctxkey.Id),
		Name:           token.Name,
		Key:            random.GenerateKey(),
		CreatedTime:    helper.GetTimestamp(),
		AccessedTime:   helper.GetTimestamp(),
		ExpiredTime:    token.ExpiredTime,
		RemainQuota:    token.RemainQuota,
		UnlimitedQuota: token.UnlimitedQuota,
		Models:         token.Models,
		Subnet:         token.Subnet,
		IpWhitelist:    token.IpWhitelist,
		BillingType:    token.BillingType,
		Advertisement:  token.Advertisement,
		AdPosition:     token.AdPosition,
		Remark:         token.Remark,
		Group:          token.Group,
	}
	// 拦截非管理员的注入广告语
	if c.GetInt("role") < common.RoleAdminUser {
		cleanToken.Advertisement = ""
		cleanToken.AdPosition = 0
	}
	// 开启事务
	tx := model.DB.Begin()
	err = cleanToken.InsertByTx(tx)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	tokenExtend.TokenId = cleanToken.Id
	err = tokenExtend.InsertByTx(tx)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	// 记录日志
	model.RecordLog(ctx, cleanToken.UserId, model.LogTypeOperation, fmt.Sprintf("创建了新令牌，令牌名称「%s」", cleanToken.Name))
	tx.Commit()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    cleanToken,
	})
	return
}

func AddTokenBatch(c *gin.Context) {
	ctx := c.Request.Context()
	var token model.Token
	var tokenExtend model.TokenExtend
	err := common.BindAndDecodeMainAndExtend(c, &token, &tokenExtend)
	batchAmount, err2 := strconv.Atoi(c.Query("batchAmount"))
	if err2 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err2.Error(),
		})
		return
	}
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if len(token.Name) > 30 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "令牌名称不能超过30位",
		})
		return
	}
	if batchAmount < 1 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "批量生成数量不能小于1个",
		})
		return
	}
	// 开启事务
	tx := model.DB.Begin()
	for i := 0; i < batchAmount; i++ {
		// 名称需要拼接索引
		cleanToken := model.Token{
			UserId:         c.GetInt("id"),
			Name:           token.Name + "_" + strconv.Itoa(i+1),
			Key:            helper.GenerateKey(),
			CreatedTime:    helper.GetTimestamp(),
			AccessedTime:   helper.GetTimestamp(),
			ExpiredTime:    token.ExpiredTime,
			RemainQuota:    token.RemainQuota,
			UnlimitedQuota: token.UnlimitedQuota,
			Models:         token.Models,
			IpWhitelist:    token.IpWhitelist,
			BillingType:    token.BillingType,
			Advertisement:  token.Advertisement,
			AdPosition:     token.AdPosition,
			Remark:         token.Remark,
			Group:          token.Group,
		}
		// 拦截非管理员的注入广告语
		if c.GetInt("role") < common.RoleAdminUser {
			cleanToken.Advertisement = ""
			cleanToken.AdPosition = 0
		}
		err = cleanToken.InsertByTx(tx)
		if err != nil {
			tx.Rollback()
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		tokenExtend.Id = 0
		tokenExtend.TokenId = cleanToken.Id
		err = tokenExtend.InsertByTx(tx)
		if err != nil {
			tx.Rollback()
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
	}
	// 记录日志
	model.RecordLog(ctx, c.GetInt("id"), model.LogTypeOperation, fmt.Sprintf("批量创建了%d个新令牌，令牌名称「%s」", batchAmount, token.Name))
	tx.Commit()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func DeleteToken(c *gin.Context) {
	ctx := c.Request.Context()
	id, _ := strconv.Atoi(c.Param("id"))
	userId := c.GetInt(ctxkey.Id)
	err := model.DeleteTokenById(id, userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	// 记录日志
	model.RecordLog(ctx, userId, model.LogTypeOperation, fmt.Sprintf("删除令牌，令牌ID%d：", id))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func DeleteTokenOrRefreshInitialToken(c *gin.Context) {
	ctx := c.Request.Context()
	id, _ := strconv.Atoi(c.Param("id"))
	userId := c.GetInt("id")
	tokenById, err2 := model.GetTokenById(id)
	if err2 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err2.Error(),
		})
		return
	}
	if tokenById == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "令牌不存在",
		})
		return
	}
	var err error
	var refreshKey string
	if tokenById.IsInitialToken {
		// 刷新初始令牌
		refreshKey, err = model.RefreshInitialToken(userId)
	} else {
		// 不是初始令牌直接删除
		err = model.DeleteTokenById(id, userId)
	}
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	// 记录日志
	if tokenById.IsInitialToken {
		model.RecordLog(ctx, userId, model.LogTypeOperation, fmt.Sprintf("刷新初始令牌，令牌ID：%d", id))
	} else {
		model.RecordLog(ctx, userId, model.LogTypeOperation, fmt.Sprintf("删除令牌，令牌ID：%d", id))
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    refreshKey,
		"message": "",
	})
	return
}

func DeleteTokenByIds(c *gin.Context) {
	ctx := c.Request.Context()
	// 解析数组
	var ids []int
	err := c.ShouldBindJSON(&ids)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}
	userId := c.GetInt("id")
	rows, err := model.DeleteTokenByIds(ids, userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	//记录日志
	model.RecordLog(ctx, userId, model.LogTypeOperation, fmt.Sprintf("批量删除了令牌，令牌ID：%v", ids))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rows,
		"message": "",
	})
	return
}

func UpdateToken(c *gin.Context) {
	ctx := c.Request.Context()
	userId := c.GetInt(ctxkey.Id)
	statusOnly := c.Query("status_only")

	var token model.Token
	var tokenExtend model.TokenExtend
	err := common.BindAndDecodeMainAndExtend(c, &token, &tokenExtend)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	err = validateToken(c, token)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("参数错误：%s", err.Error()),
		})
		return
	}

	// 处理 MJ 翻译配置的验证
	if tokenExtend.MjTranslateEnabled {
		if tokenExtend.MjTranslateBaseUrl == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "启用MJ翻译时必须提供翻译服务的基础URL",
			})
			return
		}
		if tokenExtend.MjTranslateApiKey == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "启用MJ翻译时必须提供翻译服务的API密钥",
			})
			return
		}
		if tokenExtend.MjTranslateModel == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "启用MJ翻译时必须提供翻译服务使用的模型",
			})
			return
		}
	}

	cleanToken, err := model.GetTokenByIds(token.Id, userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if token.Status == model.TokenStatusEnabled {
		if cleanToken.Status == model.TokenStatusExpired && cleanToken.ExpiredTime <= helper.GetTimestamp() && cleanToken.ExpiredTime != -1 {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "令牌已过期，请先修改令牌过期时间",
			})
			return
		}
		if cleanToken.Status == model.TokenStatusExhausted && cleanToken.RemainQuota <= 0 && !cleanToken.UnlimitedQuota {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "令牌无额度，请先修改令牌剩余额度",
			})
			return
		}
	}
	if statusOnly != "" {
		cleanToken.Status = token.Status
	} else {
		// If you add more fields, please also update token.Update()
		cleanToken.Name = token.Name
		cleanToken.ExpiredTime = token.ExpiredTime
		cleanToken.RemainQuota = token.RemainQuota
		cleanToken.UnlimitedQuota = token.UnlimitedQuota
		cleanToken.Models = token.Models
		cleanToken.Subnet = token.Subnet
		cleanToken.IpWhitelist = token.IpWhitelist
		cleanToken.Advertisement = token.Advertisement
		cleanToken.AdPosition = token.AdPosition
		cleanToken.BillingType = token.BillingType
		cleanToken.Remark = token.Remark
		cleanToken.Group = token.Group
	}
	// 拦截非管理员的注入广告语
	if c.GetInt("role") < common.RoleAdminUser {
		cleanToken.Advertisement = ""
		cleanToken.AdPosition = 0
	}

	// 开启事务
	tx := model.DB.Begin()
	err = cleanToken.UpdateByTx(tx)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	tokenExtend.TokenId = cleanToken.Id
	tokenExtend.Id = 0
	err = tokenExtend.UpdateByTx(tx)
	//记录日志
	model.RecordLog(ctx, userId, model.LogTypeOperation, fmt.Sprintf("更新了令牌「%s」 (#%s)", cleanToken.Name, strconv.Itoa(cleanToken.Id)))
	tx.Commit()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    cleanToken,
	})
	return
}
