package anthropic

import (
	"bufio"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common/ctxkey"
	outModel "github.com/songquanpeng/one-api/model"

	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/render"
	"github.com/songquanpeng/one-api/relay/meta"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/image"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/model"
)

func stopReasonClaude2OpenAI(reason *string) string {
	if reason == nil {
		return ""
	}
	switch *reason {
	case "end_turn":
		return "stop"
	case "stop_sequence":
		return "stop"
	case "max_tokens":
		return "length"
	case "tool_use":
		return "tool_calls"
	default:
		return *reason
	}
}

func ConvertRequest(textRequest model.GeneralOpenAIRequest) *Request {
	claudeTools := make([]Tool, 0, len(textRequest.Tools))

	for _, tool := range textRequest.Tools {
		if params, ok := tool.Function.Parameters.(map[string]any); ok {
			claudeTools = append(claudeTools, Tool{
				Name:        tool.Function.Name,
				Description: tool.Function.Description,
				InputSchema: InputSchema{
					Type:       params["type"].(string),
					Properties: params["properties"],
					Required:   params["required"],
				},
			})
		}
	}

	claudeRequest := Request{
		Model:       textRequest.Model,
		MaxTokens:   textRequest.MaxTokens,
		Temperature: textRequest.Temperature,
		TopP:        textRequest.TopP,
		TopK:        textRequest.TopK,
		Stream:      textRequest.Stream,
		Tools:       claudeTools,
	}

	// 处理thinking模式
	if strings.HasSuffix(textRequest.Model, "-thinking") {
		// 移除模型名称的thinking后缀
		claudeRequest.Model = strings.TrimSuffix(textRequest.Model, "-thinking")

		// 设置最小token要求（thinking模式需要更多tokens）
		if claudeRequest.MaxTokens < 1280 {
			claudeRequest.MaxTokens = 1280
		}

		// 为thinking模式设置固定参数
		temperature := 1.0
		claudeRequest.Temperature = &temperature

		// 配置thinking参数（暂时硬编码，后续可以从配置中读取）
		thinkingBudget := int(float64(claudeRequest.MaxTokens) * 0.8) // 80%的预算用于thinking
		thinking := &Thinking{
			Type:         "enabled",
			BudgetTokens: thinkingBudget,
		}
		claudeRequest.Thinking = thinking
	}

	if len(claudeTools) > 0 {
		claudeToolChoice := struct {
			Type string `json:"type"`
			Name string `json:"name,omitempty"`
		}{Type: "auto"} // default value https://docs.anthropic.com/en/docs/build-with-claude/tool-use#controlling-claudes-output
		if choice, ok := textRequest.ToolChoice.(map[string]any); ok {
			if function, ok := choice["function"].(map[string]any); ok {
				claudeToolChoice.Type = "tool"
				claudeToolChoice.Name = function["name"].(string)
			}
		} else if toolChoiceType, ok := textRequest.ToolChoice.(string); ok {
			if toolChoiceType == "any" {
				claudeToolChoice.Type = toolChoiceType
			}
		}
		claudeRequest.ToolChoice = claudeToolChoice
	}
	if claudeRequest.MaxTokens == 0 {
		claudeRequest.MaxTokens = 4096
	}
	// legacy model name mapping
	if claudeRequest.Model == "claude-instant-1" {
		claudeRequest.Model = "claude-instant-1.1"
	} else if claudeRequest.Model == "claude-2" {
		claudeRequest.Model = "claude-2.1"
	}
	for _, message := range textRequest.Messages {
		if message.Role == "system" && claudeRequest.System == "" {
			claudeRequest.System = message.StringContent()
			continue
		}
		claudeMessage := Message{
			Role: message.Role,
		}
		var content Content
		if message.IsStringContent() {
			content.Type = "text"
			content.Text = message.StringContent()
			if message.Role == "tool" {
				claudeMessage.Role = "user"
				content.Type = "tool_result"
				content.Content = content.Text
				content.Text = ""
				content.ToolUseId = message.ToolCallId
			}
			claudeMessage.Content = append(claudeMessage.Content, content)
			for i := range message.ToolCalls {
				inputParam := make(map[string]any)
				_ = json.Unmarshal([]byte(message.ToolCalls[i].Function.Arguments.(string)), &inputParam)
				claudeMessage.Content = append(claudeMessage.Content, Content{
					Type:  "tool_use",
					Id:    message.ToolCalls[i].Id,
					Name:  message.ToolCalls[i].Function.Name,
					Input: inputParam,
				})
			}
			claudeRequest.Messages = append(claudeRequest.Messages, claudeMessage)
			continue
		}
		var contents []Content
		openaiContent := message.ParseContentPro()
		for _, part := range openaiContent {
			var content Content
			if part.Type == model.ContentTypeText {
				content.Type = "text"
				content.Text = part.Text
			} else if part.Type == model.ContentTypeImageURL {
				content.Type = "image"
				content.Source = &ImageSource{
					Type: "base64",
				}
				mimeType, data, _ := image.GetImageFromUrl(part.ImageURL.Url, false)
				content.Source.MediaType = mimeType
				content.Source.Data = data
			} else if part.Type == model.ContentTypeImage {
				content.Type = part.Type
				content.Source = &ImageSource{
					Type: "base64",
				}
				if part.Source.Data != "" {
					// 切分base64,保留后面的
					if strings.Contains(part.Source.Data, "base64,") {
						split := strings.Split(part.Source.Data, "base64,")
						if len(split) > 1 {
							part.Source.Data = split[1]
						}
					}
					content.Source.Data = part.Source.Data
					content.Source.MediaType = part.Source.MediaType
				} else {
					mimeType, data, _ := image.GetImageFromUrl(part.ImageURL.Url, false)
					content.Source.MediaType = mimeType
					content.Source.Data = data
				}
			}
			contents = append(contents, content)
		}
		claudeMessage.Content = contents
		claudeRequest.Messages = append(claudeRequest.Messages, claudeMessage)
	}
	return &claudeRequest
}

func ConvertFlexibleMessageRequest(textRequest model.GeneralOpenAIRequest) *FlexibleMessageRequest {
	claudeTools := make([]Tool, 0, len(textRequest.Tools))

	for _, tool := range textRequest.Tools {
		if params, ok := tool.Function.Parameters.(map[string]any); ok {
			claudeTools = append(claudeTools, Tool{
				Name:        tool.Function.Name,
				Description: tool.Function.Description,
				InputSchema: InputSchema{
					Type:       params["type"].(string),
					Properties: params["properties"],
					Required:   params["required"],
				},
			})
		}
	}

	flexibleRequest := FlexibleMessageRequest{
		Model:       textRequest.Model,
		MaxTokens:   textRequest.MaxTokens,
		Temperature: textRequest.GetTemperature(),
		TopP:        textRequest.GetTopP(),
		TopK:        textRequest.TopK,
		Stream:      textRequest.Stream,
		Tools:       claudeTools,
	}

	// 处理 ToolChoice
	if len(claudeTools) > 0 {
		claudeToolChoice := struct {
			Type string `json:"type"`
			Name string `json:"name,omitempty"`
		}{Type: "auto"}
		if choice, ok := textRequest.ToolChoice.(map[string]any); ok {
			if function, ok := choice["function"].(map[string]any); ok {
				claudeToolChoice.Type = "tool"
				claudeToolChoice.Name = function["name"].(string)
			}
		} else if toolChoiceType, ok := textRequest.ToolChoice.(string); ok {
			if toolChoiceType == "any" {
				claudeToolChoice.Type = toolChoiceType
			}
		}
		flexibleRequest.ToolChoice = claudeToolChoice
	}

	// 设置默认 MaxTokens
	if flexibleRequest.MaxTokens == 0 {
		flexibleRequest.MaxTokens = 4096
	}

	// 模型名称映射
	if flexibleRequest.Model == "claude-instant-1" {
		flexibleRequest.Model = "claude-instant-1.1"
	} else if flexibleRequest.Model == "claude-2" {
		flexibleRequest.Model = "claude-2.1"
	}

	// 处理消息
	for _, message := range textRequest.Messages {
		if message.Role == "system" && flexibleRequest.System == "" {
			flexibleRequest.System = message.StringContent()
			continue
		}

		flexibleMessage := FlexibleMessage{
			Role: message.Role,
		}

		// 处理 content
		var content interface{}
		if message.IsStringContent() {
			content = message.StringContent()
			if message.Role == "tool" {
				flexibleMessage.Role = "user"
				content = []FlexibleContent{{
					Type:      "tool_result",
					Content:   message.StringContent(),
					ToolUseId: message.ToolCallId,
				}}
			}
		} else {
			var contentArray []FlexibleContent
			openaiContent := message.ParseContentPro()
			for _, part := range openaiContent {
				switch part.Type {
				case model.ContentTypeText:
					contentArray = append(contentArray, FlexibleContent{
						Type: "text",
						Text: part.Text,
					})
				case model.ContentTypeImageURL, model.ContentTypeImage:
					contentArray = append(contentArray, FlexibleContent{
						Type: "image",
						Source: &ImageSource{
							Type:      "base64",
							MediaType: part.Source.MediaType,
							Data:      part.Source.Data,
						},
					})
				}
			}
			content = contentArray
		}

		contentJSON, err := json.Marshal(content)
		if err != nil {
			// 处理错误，可能需要记录日志或返回错误
			continue
		}
		flexibleMessage.Content = contentJSON

		flexibleRequest.Messages = append(flexibleRequest.Messages, flexibleMessage)

		// 处理工具调用
		for _, toolCall := range message.ToolCalls {
			toolUseContent := FlexibleContent{
				Type:  "tool_use",
				Id:    toolCall.Id,
				Name:  toolCall.Function.Name,
				Input: toolCall.Function.Arguments,
			}
			toolUseJSON, err := json.Marshal([]FlexibleContent{toolUseContent})
			if err != nil {
				// 处理错误，可能需要记录日志或返回错误
				continue
			}
			toolUseMessage := FlexibleMessage{
				Role:    "user",
				Content: toolUseJSON,
			}
			flexibleRequest.Messages = append(flexibleRequest.Messages, toolUseMessage)
		}
	}

	return &flexibleRequest
}

// https://docs.anthropic.com/claude/reference/messages-streaming
func StreamResponseClaude2OpenAI(claudeResponse *StreamResponse) (*openai.ChatCompletionsStreamResponse, *Response) {
	var response *Response
	var responseText string
	var stopReason string
	tools := make([]model.Tool, 0)

	switch claudeResponse.Type {
	case "message_start":
		return nil, claudeResponse.Message
	case "content_block_start":
		if claudeResponse.ContentBlock != nil {
			responseText = claudeResponse.ContentBlock.Text
			if claudeResponse.ContentBlock.Type == "tool_use" {
				tools = append(tools, model.Tool{
					Id:   claudeResponse.ContentBlock.Id,
					Type: "function",
					Function: model.Function{
						Name:      claudeResponse.ContentBlock.Name,
						Arguments: "",
					},
				})
			}
		}
	case "content_block_delta":
		if claudeResponse.Delta != nil {
			responseText = claudeResponse.Delta.Text
			if claudeResponse.Delta.Type == "input_json_delta" {
				tools = append(tools, model.Tool{
					Function: model.Function{
						Arguments: claudeResponse.Delta.PartialJson,
					},
				})
			}
		}
	case "message_delta":
		if claudeResponse.Usage != nil {
			response = &Response{
				Usage: *claudeResponse.Usage,
			}
		}
		if claudeResponse.Delta != nil && claudeResponse.Delta.StopReason != nil {
			stopReason = *claudeResponse.Delta.StopReason
		}
	}
	var choice openai.ChatCompletionsStreamResponseChoice
	choice.Delta.Content = responseText
	if len(tools) > 0 {
		choice.Delta.Content = nil // compatible with other OpenAI derivative applications, like LobeOpenAICompatibleFactory ...
		choice.Delta.ToolCalls = tools
	}
	choice.Delta.Role = "assistant"
	finishReason := stopReasonClaude2OpenAI(&stopReason)
	if finishReason != "null" {
		choice.FinishReason = &finishReason
	}
	var openaiResponse openai.ChatCompletionsStreamResponse
	openaiResponse.Object = "chat.completion.chunk"
	openaiResponse.Choices = []openai.ChatCompletionsStreamResponseChoice{choice}
	return &openaiResponse, response
}

func ResponseClaude2OpenAI(claudeResponse *Response) *openai.TextResponse {
	var responseText string
	var thinkingContent string

	// 遍历所有内容块，分别处理不同类型
	for _, content := range claudeResponse.Content {
		switch content.Type {
		case "text":
			responseText = content.Text
		case "thinking":
			thinkingContent = content.Thinking
		}
	}

	// 如果没有找到text类型的内容，但有第一个内容块，使用第一个内容块的Text字段（向后兼容）
	if responseText == "" && len(claudeResponse.Content) > 0 {
		responseText = claudeResponse.Content[0].Text
	}

	tools := make([]model.Tool, 0)
	for _, v := range claudeResponse.Content {
		if v.Type == "tool_use" {
			args, _ := json.Marshal(v.Input)
			tools = append(tools, model.Tool{
				Id:   v.Id,
				Type: "function", // compatible with other OpenAI derivative applications
				Function: model.Function{
					Name:      v.Name,
					Arguments: string(args),
				},
			})
		}
	}

	choice := openai.TextResponseChoice{
		Index: 0,
		Message: model.Message{
			Role:             "assistant",
			Content:          responseText,
			ReasoningContent: thinkingContent,
			Name:             nil,
			ToolCalls:        tools,
		},
		FinishReason: stopReasonClaude2OpenAI(claudeResponse.StopReason),
	}

	// 构建Usage信息，包含缓存token的详细信息
	usage := model.Usage{
		PromptTokens:             claudeResponse.Usage.InputTokens,
		CompletionTokens:         claudeResponse.Usage.OutputTokens,
		TotalTokens:              claudeResponse.Usage.InputTokens + claudeResponse.Usage.OutputTokens,
		InputTokens:              claudeResponse.Usage.InputTokens,
		OutputTokens:             claudeResponse.Usage.OutputTokens,
		CacheCreationInputTokens: claudeResponse.Usage.CacheCreationInputTokens,
		CacheReadInputTokens:     claudeResponse.Usage.CacheReadInputTokens,
		ServiceTier:              claudeResponse.Usage.ServiceTier,
	}

	// 设置PromptTokensDetails，将缓存token信息转换为OpenAI格式
	if claudeResponse.Usage.CacheCreationInputTokens > 0 || claudeResponse.Usage.CacheReadInputTokens > 0 {
		cachedTokens := claudeResponse.Usage.CacheReadInputTokens
		cachedCreationTokens := claudeResponse.Usage.CacheCreationInputTokens
		usage.PromptTokensDetails = model.TokenDetails{
			CachedTokens:         &cachedTokens,
			CachedCreationTokens: &cachedCreationTokens,
		}
	}

	fullTextResponse := openai.TextResponse{
		Id:      claudeResponse.Id,
		Model:   claudeResponse.Model,
		Object:  "chat.completion",
		Created: helper.GetTimestamp(),
		Choices: []openai.TextResponseChoice{choice},
		Usage:   usage,
	}
	return &fullTextResponse
}

func StreamHandler(c *gin.Context, resp *http.Response, meta *meta.Meta, mode int) (*model.ErrorWithStatusCode, string, string, string, string, *model.Usage) {
	if meta.TransparentProxyEnabled && c.GetBool(ctxkey.IsV1MessagesPath) {
		// 透明代理走单独的逻辑 不影响老逻辑
		return ClaudeStreamHandler(c, resp, meta, mode)
	}
	var responseTextBuilder strings.Builder
	var responseFunctionCallNameBuilder strings.Builder
	var responseFunctionCallArgumentsBuilder strings.Builder
	var upstreamResponseBuilder strings.Builder
	var fullResponseBuilder strings.Builder

	// 只有在需要记录响应时才进行构建（考虑用户级别配置）
	shouldBuildUpstreamResponse := outModel.ShouldLogUpstreamResponse(meta.UserId)
	shouldBuildFullResponse := outModel.ShouldLogFullResponse(meta.UserId)
	completionId := ""
	createdTime := helper.GetTimestamp()
	scanner := bufio.NewScanner(resp.Body)
	scanner.Split(func(data []byte, atEOF bool) (advance int, token []byte, err error) {
		if atEOF && len(data) == 0 {
			return 0, nil, nil
		}
		if i := strings.Index(string(data), "\n"); i >= 0 {
			return i + 1, data[0:i], nil
		}
		if atEOF {
			return len(data), data, nil
		}
		return 0, nil, nil
	})

	common.SetEventStreamHeaders(c)

	var usage model.Usage
	var modelName string
	var id string
	var lastToolCallChoice openai.ChatCompletionsStreamResponseChoice
	// 重置局部变量inThinkingBlock，不要依赖全局变量
	inThinkingBlock := false
	usageSent := false // 标记是否已发送用量信息

	for scanner.Scan() {
		data := scanner.Text()
		if shouldBuildUpstreamResponse {
			upstreamResponseBuilder.WriteString(data + "\n")
		}
		if len(data) < 6 || !strings.HasPrefix(data, "data:") {
			continue
		}
		data = strings.TrimPrefix(data, "data:")
		data = strings.TrimSpace(data)

		var claudeResponse StreamResponse
		err := json.Unmarshal([]byte(data), &claudeResponse)
		if err != nil {
			logger.SysError("error unmarshalling stream response: " + err.Error())
			continue
		}

		// 处理思考过程(thinking)开始
		if claudeResponse.Type == "content_block_start" && claudeResponse.ContentBlock != nil && claudeResponse.ContentBlock.Type == "thinking" {
			// 思考过程开始，立即输出<think>标签
			inThinkingBlock = true

			// 发送<think>开始标签
			thinkStartResponse := &openai.ChatCompletionsStreamResponse{
				Id:      id,
				Object:  "chat.completion.chunk",
				Created: createdTime,
				Model:   modelName,
				Choices: []openai.ChatCompletionsStreamResponseChoice{
					{
						Delta: model.Message{
							Role:    "assistant",
							Content: "<think>",
						},
						Index: 0,
					},
				},
			}

			err = render.ObjectData(c, thinkStartResponse)
			if err != nil {
				logger.SysError(err.Error())
			}

			// 记录首字时间
			if meta.ResponseFirstByteTime_ == 0 {
				meta.ResponseFirstByteTime_ = helper.GetTimestamp()
			}

			responseTextBuilder.WriteString("<think>")
			continue
		}

		// 处理思考过程增量
		if claudeResponse.Type == "content_block_delta" && claudeResponse.Delta != nil && claudeResponse.Delta.Type == "thinking_delta" {
			// 思考过程增量，立即流式输出
			if claudeResponse.Delta.Thinking != "" && inThinkingBlock {
				// 直接流式输出每个thinking增量
				thinkDeltaResponse := &openai.ChatCompletionsStreamResponse{
					Id:      id,
					Object:  "chat.completion.chunk",
					Created: createdTime,
					Model:   modelName,
					Choices: []openai.ChatCompletionsStreamResponseChoice{
						{
							Delta: model.Message{
								Role:    "assistant",
								Content: claudeResponse.Delta.Thinking,
							},
							Index: 0,
						},
					},
				}

				err = render.ObjectData(c, thinkDeltaResponse)
				if err != nil {
					logger.SysError(err.Error())
				}

				// 记录首字时间
				if meta.ResponseFirstByteTime_ == 0 {
					meta.ResponseFirstByteTime_ = helper.GetTimestamp()
				}

				responseTextBuilder.WriteString(claudeResponse.Delta.Thinking)
			}
			continue
		}

		// 处理思考过程结束
		if (claudeResponse.Type == "content_block_stop" || claudeResponse.Type == "content_block_start") && inThinkingBlock {
			// 思考过程结束，输出</think>标签
			inThinkingBlock = false

			thinkEndResponse := &openai.ChatCompletionsStreamResponse{
				Id:      id,
				Object:  "chat.completion.chunk",
				Created: createdTime,
				Model:   modelName,
				Choices: []openai.ChatCompletionsStreamResponseChoice{
					{
						Delta: model.Message{
							Role:    "assistant",
							Content: "</think>",
						},
						Index: 0,
					},
				},
			}

			err = render.ObjectData(c, thinkEndResponse)
			if err != nil {
				logger.SysError(err.Error())
			}

			responseTextBuilder.WriteString("</think>")

			// 如果是新的内容块开始，需要继续处理这个事件
			if claudeResponse.Type == "content_block_start" {
				// 处理后续的普通内容块
				response, metaResp := StreamResponseClaude2OpenAI(&claudeResponse)
				if metaResp != nil {
					usage.PromptTokens += metaResp.Usage.InputTokens
					usage.CompletionTokens += metaResp.Usage.OutputTokens
					if len(metaResp.Id) > 0 {
						modelName = metaResp.Model
						id = fmt.Sprintf("chatcmpl-%s", metaResp.Id)
					} else if len(lastToolCallChoice.Delta.ToolCalls) > 0 {
						lastArgs := &lastToolCallChoice.Delta.ToolCalls[len(lastToolCallChoice.Delta.ToolCalls)-1].Function
						if len(lastArgs.Arguments.(string)) == 0 {
							lastArgs.Arguments = "{}"
							if response != nil && len(response.Choices) > 0 {
								response.Choices[len(response.Choices)-1].Delta.Content = nil
								response.Choices[len(response.Choices)-1].Delta.ToolCalls = lastToolCallChoice.Delta.ToolCalls
							}
						}
					}
				}

				if response != nil {
					response.Id = id
					response.Model = modelName
					response.Created = createdTime

					for _, choice := range response.Choices {
						if len(choice.Delta.ToolCalls) > 0 {
							lastToolCallChoice = choice
						}
					}

					// 记录首字时间
					if meta.ResponseFirstByteTime_ == 0 {
						meta.ResponseFirstByteTime_ = helper.GetTimestamp()
					}

					responseTextBuilder.WriteString(response.Choices[0].Delta.StringContent())

					// 校验敏感词
					isSensitive, word := filterNonStreamSensitiveWords(response.Choices[0].Delta.StringContent())
					if isSensitive {
						// 记录敏感词命中
						err = outModel.RecordSensitiveWordHit(meta.DeepCopyToLogMeta(), word, meta.DetailPrompt, response.GetFirstChoiceDeltaContent())
						if err != nil {
							logger.SysError("record sensitive word hit failed: " + err.Error())
						}
						response.SetFirstChoiceDeltaContent(word)
						err = render.ObjectData(c, response)
						return openai.ErrorWrapper(errors.New(config.SensitiveWordsTips), "sensitive_words_in_request", http.StatusBadRequest), responseTextBuilder.String(), "", "", "", &usage
					}

					err = render.ObjectData(c, response)
					if err != nil {
						logger.SysError(err.Error())
					}
				}
			}

			// 处理message_delta类型，这通常是流结束时包含用量信息的消息
			if claudeResponse.Type == "message_delta" && claudeResponse.Usage != nil && !usageSent {
				// 更新用量统计
				usage.PromptTokens += claudeResponse.Usage.InputTokens
				usage.CompletionTokens += claudeResponse.Usage.OutputTokens
				usage.TotalTokens = usage.PromptTokens + usage.CompletionTokens
				// 添加cache相关字段的处理
				usage.CacheCreationInputTokens += claudeResponse.Usage.CacheCreationInputTokens
				usage.CacheReadInputTokens += claudeResponse.Usage.CacheReadInputTokens
				if claudeResponse.Usage.ServiceTier != "" {
					usage.ServiceTier = claudeResponse.Usage.ServiceTier
				}

				// 构造OpenAI格式的用量响应
				usageResponse := &openai.ChatCompletionsStreamResponse{
					Id:      id,
					Object:  "chat.completion.chunk",
					Created: createdTime,
					Model:   modelName,
					Choices: []openai.ChatCompletionsStreamResponseChoice{
						{
							Delta: model.Message{
								Role:    "assistant",
								Content: "", // 用量消息内容为空
							},
							Index: 0,
						},
					},
					Usage: &model.Usage{
						PromptTokens:             usage.PromptTokens,
						CompletionTokens:         usage.CompletionTokens,
						TotalTokens:              usage.TotalTokens,
						InputTokens:              usage.PromptTokens,
						OutputTokens:             usage.CompletionTokens,
						CacheCreationInputTokens: usage.CacheCreationInputTokens,
						CacheReadInputTokens:     usage.CacheReadInputTokens,
						ServiceTier:              usage.ServiceTier,
					},
				}

				// 发送用量信息
				err = render.ObjectData(c, usageResponse)
				if err != nil {
					logger.SysError(err.Error())
				}

				usageSent = true // 标记已发送用量信息
			}

			continue
		}

		// 处理常规内容
		response, metaResp := StreamResponseClaude2OpenAI(&claudeResponse)
		if metaResp != nil {
			usage.PromptTokens += metaResp.Usage.InputTokens
			usage.CompletionTokens += metaResp.Usage.OutputTokens
			// 添加cache相关字段的处理
			usage.CacheCreationInputTokens += metaResp.Usage.CacheCreationInputTokens
			usage.CacheReadInputTokens += metaResp.Usage.CacheReadInputTokens
			if metaResp.Usage.ServiceTier != "" {
				usage.ServiceTier = metaResp.Usage.ServiceTier
			}
			if len(metaResp.Id) > 0 { // only message_start has an id, otherwise it's a finish_reason event.
				modelName = metaResp.Model
				id = fmt.Sprintf("chatcmpl-%s", metaResp.Id)
				continue
			} else { // finish_reason case
				if len(lastToolCallChoice.Delta.ToolCalls) > 0 {
					lastArgs := &lastToolCallChoice.Delta.ToolCalls[len(lastToolCallChoice.Delta.ToolCalls)-1].Function
					if len(lastArgs.Arguments.(string)) == 0 { // compatible with OpenAI sending an empty object `{}` when no arguments.
						lastArgs.Arguments = "{}"
						response.Choices[len(response.Choices)-1].Delta.Content = nil
						response.Choices[len(response.Choices)-1].Delta.ToolCalls = lastToolCallChoice.Delta.ToolCalls
					}
				}
			}
		}
		if response == nil {
			continue
		}

		response.Id = id
		response.Model = modelName
		response.Created = createdTime

		for _, choice := range response.Choices {
			if len(choice.Delta.ToolCalls) > 0 {
				lastToolCallChoice = choice
			}
		}
		// 记录首字时间
		if meta.ResponseFirstByteTime_ == 0 {
			meta.ResponseFirstByteTime_ = helper.GetTimestamp()
		}
		responseTextBuilder.WriteString(response.Choices[0].Delta.StringContent())
		// 校验敏感词
		isSensitive, word := filterNonStreamSensitiveWords(response.Choices[0].Delta.StringContent())
		if isSensitive {
			// 记录敏感词命中
			err = outModel.RecordSensitiveWordHit(meta.DeepCopyToLogMeta(), word, meta.DetailPrompt, response.GetFirstChoiceDeltaContent())
			if err != nil {
				logger.SysError("record sensitive word hit failed: " + err.Error())
			}
			response.SetFirstChoiceDeltaContent(word)
			err = render.ObjectData(c, response)
			return openai.ErrorWrapper(errors.New(config.SensitiveWordsTips), "sensitive_words_in_request", http.StatusBadRequest), responseTextBuilder.String(), "", "", "", &usage
		}
		err = render.ObjectData(c, response)
		if err != nil {
			logger.SysError(err.Error())
		}
	}

	// 如果到流结束还没发送过用量信息，确保发送
	if !usageSent && (usage.PromptTokens > 0 || usage.CompletionTokens > 0) {
		usage.TotalTokens = usage.PromptTokens + usage.CompletionTokens
		usage.InputTokens = usage.PromptTokens
		usage.OutputTokens = usage.CompletionTokens

		// 构造OpenAI格式的用量响应
		usageResponse := &openai.ChatCompletionsStreamResponse{
			Id:      id,
			Object:  "chat.completion.chunk",
			Created: createdTime,
			Model:   modelName,
			Choices: []openai.ChatCompletionsStreamResponseChoice{
				{
					Delta: model.Message{
						Role:    "assistant",
						Content: "", // 用量消息内容为空
					},
					Index: 0,
				},
			},
			Usage: &model.Usage{
				PromptTokens:             usage.PromptTokens,
				CompletionTokens:         usage.CompletionTokens,
				TotalTokens:              usage.TotalTokens,
				InputTokens:              usage.InputTokens,
				OutputTokens:             usage.OutputTokens,
				CacheCreationInputTokens: usage.CacheCreationInputTokens,
				CacheReadInputTokens:     usage.CacheReadInputTokens,
				ServiceTier:              usage.ServiceTier,
			},
		}

		// 发送用量信息
		err := render.ObjectData(c, usageResponse)
		if err != nil {
			logger.SysError(err.Error())
		}
	}

	if err := scanner.Err(); err != nil {
		logger.SysError("error reading stream: " + err.Error())
	}

	render.Done(c)
	// 只在需要时保存响应
	if shouldBuildUpstreamResponse {
		meta.UpstreamResponse = upstreamResponseBuilder.String()
	}
	if shouldBuildFullResponse {
		meta.FullResponse = fullResponseBuilder.String()
	}
	err := resp.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), responseTextBuilder.String(), "", "", "", &usage
	}
	return nil, responseTextBuilder.String(), responseFunctionCallNameBuilder.String(), responseFunctionCallArgumentsBuilder.String(), completionId, &usage
}

func Handler(c *gin.Context, resp *http.Response, meta *meta.Meta, promptTokens int, modelName string) (*model.ErrorWithStatusCode, *model.Usage) {
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return openai.ErrorWrapper(err, "read_response_body_failed", http.StatusInternalServerError), nil
	}
	err = resp.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), nil
	}
	var claudeResponse Response
	err = json.Unmarshal(responseBody, &claudeResponse)
	if err != nil {
		return openai.ErrorWrapper(err, "unmarshal_response_body_failed", http.StatusInternalServerError), nil
	}
	if claudeResponse.Error != nil && claudeResponse.Error.Type != "" {
		return &model.ErrorWithStatusCode{
			Error: model.Error{
				Message: claudeResponse.Error.Message,
				Type:    claudeResponse.Error.Type,
				Param:   "",
				Code:    claudeResponse.Error.Type,
			},
			StatusCode: resp.StatusCode,
		}, nil
	}
	fullTextResponse := ResponseClaude2OpenAI(&claudeResponse)
	fullTextResponse.Model = modelName
	usage := model.Usage{
		PromptTokens:             claudeResponse.Usage.InputTokens,
		CompletionTokens:         claudeResponse.Usage.OutputTokens,
		TotalTokens:              claudeResponse.Usage.InputTokens + claudeResponse.Usage.OutputTokens,
		InputTokens:              claudeResponse.Usage.InputTokens,
		OutputTokens:             claudeResponse.Usage.OutputTokens,
		CacheCreationInputTokens: claudeResponse.Usage.CacheCreationInputTokens,
		CacheReadInputTokens:     claudeResponse.Usage.CacheReadInputTokens,
		ServiceTier:              claudeResponse.Usage.ServiceTier,
	}
	fullTextResponse.Usage = usage

	// 过滤敏感词
	isSensitive, word := filterNonStreamSensitiveWords(fullTextResponse.GetFirstChoiceMessage())
	if isSensitive {
		fullTextResponse.SetFirstChoiceMessage(word)
	}
	jsonResponse, err := json.Marshal(fullTextResponse)
	if err != nil {
		return openai.ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil
	}
	// 判断是否开启透明代理
	transparentProxyEnabled := c.GetBool("transparent_proxy_enabled")
	if transparentProxyEnabled {
		jsonResponse, err = json.Marshal(claudeResponse)
		if err != nil {
			return openai.ErrorWrapper(err, "marshal_transparent_response_body_failed", http.StatusInternalServerError), nil
		}
	}
	c.Writer.Header().Set("Content-Type", "application/json")
	c.Writer.WriteHeader(resp.StatusCode)
	_, err = c.Writer.Write(jsonResponse)
	meta.ResponseFirstByteTime_ = helper.GetTimestamp()
	return nil, &usage
}

func HandlerWithResponse(c *gin.Context, resp *http.Response, meta *meta.Meta, promptTokens int, requestModel string) (*model.ErrorWithStatusCode, *model.Usage, string, string) {
	// 判断是否开启透明代理
	transparentProxyEnabled := c.GetBool("transparent_proxy_enabled")
	// 是否是v1_message
	isV1MessagesPath := c.GetBool(ctxkey.IsV1MessagesPath)
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return openai.ErrorWrapper(err, "read_response_body_failed", http.StatusInternalServerError), nil, "", ""
	}
	err = resp.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), nil, "", ""
	}
	var claudeResponse Response
	err = json.Unmarshal(responseBody, &claudeResponse)
	if err != nil {
		return openai.ErrorWrapper(err, "unmarshal_response_body_failed", http.StatusInternalServerError), nil, "", ""
	}
	if outModel.ShouldLogDetail(meta.UserId) && config.LogUpstreamResponseEnabled {
		// 在所有处理完成后，保存最终要返回给客户端的响应
		meta.UpstreamResponse = string(responseBody)
	}
	if claudeResponse.Error != nil && claudeResponse.Error.Type != "" {
		return &model.ErrorWithStatusCode{
			Error: model.Error{
				Message: claudeResponse.Error.Message,
				Type:    claudeResponse.Error.Type,
				Param:   "",
				Code:    claudeResponse.Error.Type,
			},
			StatusCode: resp.StatusCode,
		}, nil, "", ""
	}
	fullTextResponse := ResponseClaude2OpenAI(&claudeResponse)
	usage := model.Usage{
		PromptTokens:             claudeResponse.Usage.InputTokens,
		CompletionTokens:         claudeResponse.Usage.OutputTokens,
		TotalTokens:              claudeResponse.Usage.InputTokens + claudeResponse.Usage.OutputTokens,
		InputTokens:              claudeResponse.Usage.InputTokens,
		OutputTokens:             claudeResponse.Usage.OutputTokens,
		CacheCreationInputTokens: claudeResponse.Usage.CacheCreationInputTokens,
		CacheReadInputTokens:     claudeResponse.Usage.CacheReadInputTokens,
		ServiceTier:              claudeResponse.Usage.ServiceTier,
	}
	if !isV1MessagesPath {
		if claudeResponse.Usage.CacheCreationInputTokens != 0 {
			usage.PromptTokensDetails.CachedCreationTokens = &claudeResponse.Usage.CacheCreationInputTokens
		}
		if claudeResponse.Usage.CacheReadInputTokens != 0 {
			usage.PromptTokensDetails.CachedTokens = &claudeResponse.Usage.CacheReadInputTokens
		}
	}
	fullTextResponse.Usage = usage

	// 过滤敏感词
	isSensitive, word := filterNonStreamSensitiveWords(fullTextResponse.GetFirstChoiceMessage())
	if isSensitive {
		fullTextResponse.SetFirstChoiceMessage(word)
	}
	jsonResponse, err := json.Marshal(fullTextResponse)
	if err != nil {
		return openai.ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil, "", ""
	}

	if transparentProxyEnabled && isV1MessagesPath {
		// 使用动态JSON处理，保持原始字段结构的同时允许修改
		var rawResponse map[string]interface{}
		err = json.Unmarshal(responseBody, &rawResponse)
		if err != nil {
			return openai.ErrorWrapper(err, "unmarshal_raw_response_failed", http.StatusInternalServerError), nil, "", ""
		}

		// 在这里可以修改特定字段，例如：
		// if usage, ok := rawResponse["usage"].(map[string]interface{}); ok {
		//     // 修改usage中的某些字段
		//     usage["custom_field"] = "custom_value"
		//     // 或者修改现有字段
		//     // usage["output_tokens"] = someModifiedValue
		// }
		//
		// 也可以修改其他字段：
		// rawResponse["custom_response_field"] = "some_value"

		jsonResponse, err = json.Marshal(rawResponse)
		if err != nil {
			return openai.ErrorWrapper(err, "marshal_transparent_response_body_failed", http.StatusInternalServerError), nil, "", ""
		}
	}
	c.Writer.Header().Set("Content-Type", "application/json")
	c.Writer.WriteHeader(resp.StatusCode)
	_, err = c.Writer.Write(jsonResponse)
	meta.ResponseFirstByteTime_ = helper.GetTimestamp()
	if outModel.ShouldLogFullResponse(meta.UserId) {
		// 在所有处理完成后，保存最终要返回给客户端的响应（考虑用户级别配置）
		meta.FullResponse = string(jsonResponse)
	}
	// 根据透明代理模式决定返回的 detailCompletion
	var detailCompletion string
	if transparentProxyEnabled && isV1MessagesPath {
		// 透明代理模式下，从原始 Claude 响应中提取文本内容
		for _, content := range claudeResponse.Content {
			if content.Type == "text" {
				detailCompletion += content.Text
			}
		}
		// 如果没有找到text类型的内容，但有第一个内容块，使用第一个内容块的Text字段（向后兼容）
		if detailCompletion == "" && len(claudeResponse.Content) > 0 {
			detailCompletion = claudeResponse.Content[0].Text
		}
	} else {
		// 非透明代理模式下，使用转换后的 OpenAI 格式响应
		detailCompletion = fullTextResponse.GetFirstChoiceMessage()
	}

	return nil, &usage, detailCompletion, lo.If(transparentProxyEnabled, claudeResponse.Id).Else(fullTextResponse.Id)
}

// claude流式返回
func ClaudeStreamHandler(c *gin.Context, resp *http.Response, meta *meta.Meta, mode int) (*model.ErrorWithStatusCode, string, string, string, string, *model.Usage) {
	var responseTextBuilder strings.Builder
	var responseFunctionCallNameBuilder strings.Builder
	var responseFunctionCallArgumentsBuilder strings.Builder
	var upstreamResponseBuilder strings.Builder
	var fullResponseBuilder strings.Builder

	// 只有在需要记录响应时才进行构建（考虑用户级别配置）
	shouldBuildUpstreamResponse := outModel.ShouldLogUpstreamResponse(meta.UserId)
	shouldBuildFullResponse := outModel.ShouldLogFullResponse(meta.UserId)
	completionId := ""
	//createdTime := helper.GetTimestamp()
	scanner := bufio.NewScanner(resp.Body)
	scanner.Split(func(data []byte, atEOF bool) (advance int, token []byte, err error) {
		if atEOF && len(data) == 0 {
			return 0, nil, nil
		}
		if i := strings.Index(string(data), "\n"); i >= 0 {
			return i + 1, data[0:i], nil
		}
		if atEOF {
			return len(data), data, nil
		}
		return 0, nil, nil
	})

	common.SetEventStreamHeaders(c)

	var usage model.Usage
	var modelName string
	//var id string
	//var lastToolCallChoice openai.ChatCompletionsStreamResponseChoice

	// 标记是否已经从API获取到token计数
	var inputTokensFromAPI bool
	var outputTokensFromAPI bool

	// 收集完整响应文本用于可能的token计算
	var thinkingText string

	for scanner.Scan() {
		data := ""
		originData := scanner.Text()
		// 只在需要时记录上游响应
		if shouldBuildUpstreamResponse {
			upstreamResponseBuilder.WriteString(originData + "\n")
		}
		if !strings.HasPrefix(originData, "data:") {
			render.OriginStringData(c, originData)
			if shouldBuildFullResponse {
				fullResponseBuilder.WriteString(originData + "\n")
			}
			continue
		}
		data = strings.TrimPrefix(originData, "data:")
		data = strings.TrimSpace(data)

		var claudeResponse StreamResponse
		err := json.Unmarshal([]byte(data), &claudeResponse)
		if err != nil {
			logger.SysError("error unmarshalling stream response: " + err.Error())
			render.OriginStringData(c, originData)
			if shouldBuildFullResponse {
				fullResponseBuilder.WriteString(originData + "\n")
			}
			continue
		}

		// 从 message_start 事件中提取 input_tokens 和初始的 output_tokens
		if claudeResponse.Type == "message_start" && claudeResponse.Message != nil {
			if claudeResponse.Message.Usage.InputTokens > 0 {
				usage.PromptTokens = claudeResponse.Message.Usage.InputTokens
				inputTokensFromAPI = true
			}
			if claudeResponse.Message.Usage.OutputTokens > 0 {
				usage.CompletionTokens = claudeResponse.Message.Usage.OutputTokens
				outputTokensFromAPI = true
			}
			// 添加cache相关字段的处理
			usage.CacheCreationInputTokens += claudeResponse.Message.Usage.CacheCreationInputTokens
			usage.CacheReadInputTokens += claudeResponse.Message.Usage.CacheReadInputTokens
			if claudeResponse.Message.Usage.ServiceTier != "" {
				usage.ServiceTier = claudeResponse.Message.Usage.ServiceTier
			}
			modelName = claudeResponse.Message.Model
		}

		// 从 message_delta 事件中更新最终的 output_tokens
		if claudeResponse.Type == "message_delta" && claudeResponse.Usage != nil {
			if claudeResponse.Usage.OutputTokens > 0 {
				usage.CompletionTokens = claudeResponse.Usage.OutputTokens
				outputTokensFromAPI = true
			}
			// 添加cache相关字段的处理
			usage.CacheCreationInputTokens += claudeResponse.Usage.CacheCreationInputTokens
			usage.CacheReadInputTokens += claudeResponse.Usage.CacheReadInputTokens
			if claudeResponse.Usage.ServiceTier != "" {
				usage.ServiceTier = claudeResponse.Usage.ServiceTier
			}
		}

		// 处理思考过程(thinking)和其他内容类型
		if claudeResponse.Type == "content_block_start" && claudeResponse.ContentBlock != nil && claudeResponse.ContentBlock.Type == "thinking" {
			// 思考过程开始，直接传递给客户端
			render.OriginStringData(c, originData)
			if shouldBuildFullResponse {
				fullResponseBuilder.WriteString(originData + "\n")
			}
			continue
		}

		if claudeResponse.Type == "content_block_delta" && claudeResponse.Delta != nil && claudeResponse.Delta.Type == "thinking_delta" {
			// 思考过程增量，直接传递给客户端
			// 收集思考文本用于可能需要的自主计算
			if claudeResponse.Delta.Thinking != "" {
				thinkingText += claudeResponse.Delta.Thinking

				// 记录首字时间
				if meta.ResponseFirstByteTime_ == 0 {
					meta.ResponseFirstByteTime_ = helper.GetTimestamp()
				}
			}
			render.OriginStringData(c, originData)
			if shouldBuildFullResponse {
				fullResponseBuilder.WriteString(originData + "\n")
			}
			continue
		}

		// 处理普通文本内容增量
		if claudeResponse.Type == "content_block_delta" && claudeResponse.Delta != nil && claudeResponse.Delta.Type == "text_delta" {
			// 收集文本内容用于日志记录
			if claudeResponse.Delta.Text != "" {
				responseTextBuilder.WriteString(claudeResponse.Delta.Text)

				// 记录首字时间
				if meta.ResponseFirstByteTime_ == 0 {
					meta.ResponseFirstByteTime_ = helper.GetTimestamp()
				}
			}
			render.OriginStringData(c, originData)
			if shouldBuildFullResponse {
				fullResponseBuilder.WriteString(originData + "\n")
			}
			continue
		}

		// 处理签名增量
		if claudeResponse.Type == "content_block_delta" && claudeResponse.Delta != nil && claudeResponse.Delta.Type == "signature_delta" {
			render.OriginStringData(c, originData)
			if shouldBuildFullResponse {
				fullResponseBuilder.WriteString(originData + "\n")
			}
			continue
		}

		// 处理内容块停止
		if claudeResponse.Type == "content_block_stop" {
			render.OriginStringData(c, originData)
			if shouldBuildFullResponse {
				fullResponseBuilder.WriteString(originData + "\n")
			}
			continue
		}

		// 处理 ping 事件
		if claudeResponse.Type == "ping" {
			render.OriginStringData(c, originData)
			if shouldBuildFullResponse {
				fullResponseBuilder.WriteString(originData + "\n")
			}
			continue
		}

		render.OriginStringData(c, originData)
		if shouldBuildFullResponse {
			fullResponseBuilder.WriteString(originData + "\n")
		}
	}

	if err := scanner.Err(); err != nil {
		logger.SysError("error reading stream: " + err.Error())
	}

	err := resp.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), responseTextBuilder.String(), "", "", "", &usage
	}

	// 如果API没有提供token计数，使用CountTokenText或CountTokenTextNew函数计算
	userId := meta.UserId

	if !inputTokensFromAPI {
		if meta.PromptTokens > 0 {
			usage.PromptTokens = meta.PromptTokens
		} else {
			// 可能需要从请求中计算，取决于项目结构
			logger.SysWarn("input_tokens not provided by API, using meta.PromptTokens")
		}
	}

	if !outputTokensFromAPI {
		// 计算文本输出的token数
		completionText := responseTextBuilder.String()
		if thinkingText != "" {
			// 如果有思考过程文本，也计算它的token
			completionText += thinkingText
		}

		if completionText != "" {
			// 使用参考的token计算方法
			usage.CompletionTokens = lo.If(outModel.GetNewTikTokenBilling(userId),
				openai.CountTokenTextNew(completionText, modelName)).
				Else(openai.CountTokenText(completionText, modelName))
		}
	}

	// 设置 total_tokens 和其他相关字段
	usage.TotalTokens = usage.PromptTokens + usage.CompletionTokens
	usage.InputTokens = usage.PromptTokens
	usage.OutputTokens = usage.CompletionTokens
	// 只在需要时保存响应
	if shouldBuildUpstreamResponse {
		meta.UpstreamResponse = upstreamResponseBuilder.String()
	}
	if shouldBuildFullResponse {
		meta.FullResponse = fullResponseBuilder.String()
	}
	return nil, responseTextBuilder.String(), responseFunctionCallNameBuilder.String(), responseFunctionCallArgumentsBuilder.String(), completionId, &usage
}
